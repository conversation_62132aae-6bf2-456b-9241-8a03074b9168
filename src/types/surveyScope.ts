/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { MetricGroup } from './metricGroup';
import { StakeholderGroup } from '../model/stakeholderGroup';
import { Group } from '@g17eco/core';
import { AppConfig } from './app';
import { ScopeQuestionOptionalValue, SurveyScopeFilters, UpdateScopeFiltersType } from '@g17eco/types/survey';
import { SurveyActionData } from '@g17eco/types/surveyAction';
import { UserMin } from '@g17eco/types/user';
import { SurveyUserRoles } from '@constants/user';
import { ViewValues } from './surveyOverview';
import { DelegationScopeOptions } from './surveyVirtualScope';
import { CardGridButtonProps } from '../features/survey-scope/CardGrid';

export interface Breadcrumb {
  cardGroup: ViewValues,
  cardCategory: string
  title: string,
}

export interface UNSDGMap {
  goals: UNSDGMap_Goal[]
}

export interface UNSDGMap_Goal {
  code: string,
  title: string,
  description: string,
  targets: UNSDGMap_Target[]
}

export interface UNSDGMap_Target {
  code: string,
  title: string,
  description: string,
}

export type handleDrilldownInterface = (
  cardGroup: ViewValues,
  cardCategory: string,
  title: string,
  groupBy?: ViewValues,
) => void;

export interface CardProps {
  appSettings: AppConfig['settings']
  questionList: ScopeQuestionOptionalValue[];
  UNSDGMap: UNSDGMap;
  materiality?: {
    [key: string]: number
  };
  surveyData: Pick<SurveyActionData, '_id' | 'scope' | 'scopeConfig' | 'initiativeId'>;
  blueprint: any;
  view: ViewValues;
  cardGroup: ViewValues;
  scopeType: ViewValues | DelegationScopeOptions;
  addBtn: (scopeTag: string, name?: string, group?: Group) => CardGridButtonProps;
  breadcrumbs: Breadcrumb[],
  metricGroups: MetricGroup[],
  handleDrilldown: handleDrilldownInterface;
  handleToggleDelegation?: (e: React.MouseEvent<HTMLButtonElement>, userRole: SurveyUserRoles) => void;
  users: UserMin[],
  surveyStakeholders?: StakeholderGroup,
  isUserDelegated?: (userId: string, userRole?: string) => boolean,
  handleCreateCustomMetric?: () => void;
  selectedView?: ViewValues;
  setSelectedView?: (view: ViewValues) => void;
  scopeFilters: SurveyScopeFilters;
  updateScopeFilters: UpdateScopeFiltersType;
  // used for determining which cards to show in CORE section for restricted tiers
  // optional as it's only used in StandardsAndFrameworksCards -> getQuestionPackCards
  isRestricted?: boolean;
}
