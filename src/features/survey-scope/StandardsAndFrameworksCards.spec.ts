import { describe, it, expect, vi, beforeEach } from 'vitest';
import { faker } from '@faker-js/faker';
import { CardGridItemProps } from './CardGrid';
import { PackAccumulator, filterCombinedCards, getQuestionPackCards } from './StandardsAndFrameworksCards';
import { CardProps } from '@g17eco/types/surveyScope';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { CardGroupName } from './utils';
import { SurveyScope } from '@g17eco/types/surveyCommon';
import { getDefaultScope } from '@utils/survey-scope';
import { CustomScope } from '@g17eco/types/initiative';
import { createSurveyData } from '@fixtures/survey-factory';

interface CombinedCardsProvider {
  description: string;
  inputCards: CardGridItemProps[];
  expectedCombinedCards: PackAccumulator;
}

// Mock only the essential dependencies - let the rest work naturally
vi.mock('@g17eco/core', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@g17eco/core')>();
  return {
    ...actual,
    standards: {
      gri2021: {
        code: 'gri2021',
        name: 'GRI 2021',
        subtitle: 'GRI Standards 2021',
        src: '/gri-logo.png',
        subgroups: [],
        requiredTags: undefined,
        hidden: false,
        link: 'https://gri.org',
      },
      sasb: {
        code: 'sasb',
        name: 'SASB',
        subtitle: 'Sustainability Accounting Standards Board',
        src: '/sasb-logo.png',
        subgroups: [],
        requiredTags: undefined,
        hidden: false,
      },
    },
    frameworks: {
      tcfd: {
        code: 'tcfd',
        name: 'TCFD',
        subtitle: 'Task Force on Climate-related Financial Disclosures',
        src: '/tcfd-logo.png',
        subgroups: [],
        requiredTags: ['premium'],
        hidden: false,
      },
    },
    regulatory: {},
    ratings: {},
  };
});

const mockCardGridItemProps: CardGridItemProps = {
  key: faker.lorem.words(),
  inScope: faker.datatype.boolean(),
  isPartial: faker.datatype.boolean(),
  title: faker.lorem.words(),
  sortTitle: faker.lorem.words(),
  scopeTag: faker.lorem.words(),
  version: undefined,
  versionGroupCode: undefined,
};

describe('filterCombinedCards', () => {
  const combinedCardsProvider: CombinedCardsProvider[] = [
    {
      description:
        'when modules from the same versionGroupCode have different versions then result should be the latest version',
      inputCards: [
        {
          ...mockCardGridItemProps,
          versionGroupCode: 'gri',
          version: 2020,
          scopeTag: 'gri',
        },
        {
          ...mockCardGridItemProps,
          versionGroupCode: 'gri',
          version: 2021,
          scopeTag: 'gri2021',
        },
      ],
      expectedCombinedCards: {
        latest: [
          {
            ...mockCardGridItemProps,
            versionGroupCode: 'gri',
            version: 2021,
            scopeTag: 'gri2021',
          },
        ],
        archived: [
          {
            ...mockCardGridItemProps,
            versionGroupCode: 'gri',
            version: 2020,
            scopeTag: 'gri',
          },
        ],
        latestMap: {
          gri: {
            ...mockCardGridItemProps,
            versionGroupCode: 'gri',
            version: 2021,
            scopeTag: 'gri2021',
          },
        },
      },
    },
    {
      description:
      'when any modules have no version or versionGroupCode then they should be included in the result',
      inputCards: [
        {
          ...mockCardGridItemProps,
          scopeTag: 'gri',
        },
        {
          ...mockCardGridItemProps,
          scopeTag: 'sec',
        },
      ],
      expectedCombinedCards: {
        latest: [
          {
            ...mockCardGridItemProps,
            scopeTag: 'gri',
          },
          {
            ...mockCardGridItemProps,
            scopeTag: 'sec',
          },
        ],
        archived: [],
        latestMap: {
          gri: {
            ...mockCardGridItemProps,
            scopeTag: 'gri',
          },
          sec: {
            ...mockCardGridItemProps,
            scopeTag: 'sec',
          },
        },
      },
    },
    {
      description: 'when multiple modules have the same version and versionGroupCode then latest should be the last one',
      inputCards: [
        {
          ...mockCardGridItemProps,
          version: 2020,
          versionGroupCode: 'tcfd',
          scopeTag: 'tcfd',
        },
        {
          ...mockCardGridItemProps,
          version: 2020,
          versionGroupCode: 'tcfd',
          scopeTag: 'tcfd_standard',
        },
      ],
      expectedCombinedCards: {
        latest: [
          {
            ...mockCardGridItemProps,
            version: 2020,
            versionGroupCode: 'tcfd',
            scopeTag: 'tcfd',
          },
        ],
        archived: [
          {
            ...mockCardGridItemProps,
            version: 2020,
            versionGroupCode: 'tcfd',
            scopeTag: 'tcfd_standard',
          },
        ],
        latestMap: {
          tcfd: {
            ...mockCardGridItemProps,
            version: 2020,
            versionGroupCode: 'tcfd',
            scopeTag: 'tcfd',
          },
        },
      },
    },
  ];
  combinedCardsProvider.forEach(({ description, inputCards, expectedCombinedCards }) => {
    const input = `[${inputCards.map((card) => card.scopeTag)}]`;
    const expected = `latest: [${expectedCombinedCards.latest.map(
      (card) => card.scopeTag
    )}], archived: [${expectedCombinedCards.archived.map((card) => card.scopeTag)}]`;
    test(`${description}, input: ${input}, expected: ${expected}`, () => {

      const filteredCombinedCards = filterCombinedCards(inputCards);
      expect(filteredCombinedCards.latest).toEqual(expectedCombinedCards.latest);
      expect(filteredCombinedCards.archived).toEqual(expectedCombinedCards.archived);
    });
  });
});

describe('getQuestionPackCards', () => {
  const mockScopeTag1 = 'gri2021';
  const mockScopeTag2 = 'tcfd';
  const mockScopeTag3 = 'sasb';

  const createMockCard = (scopeTag: string, requiredTags?: string[]): CardGridItemProps => ({
    key: `card-${scopeTag}`,
    title: `${scopeTag} Card`,
    sortTitle: scopeTag,
    scopeTag,
    inScope: false,
    isPartial: false,
    requiredTags,
  });

  const createMockScopeConfig = (code: string, required: boolean): CustomScope => ({
    scopeType: 'standards',
    code,
    required,
  });

  // Single question with all scope tags
  const mockQuestion = {
    _id: 'question-multi-scope',
    tags: [mockScopeTag1, mockScopeTag2, mockScopeTag3],
    code: 'Q-MULTI',
    question: 'Mock question for all scopes',
    universalTracker: {
      hasAlternativeInfo: vi.fn(() => false),
      getTypeTags: vi.fn(() => [mockScopeTag1, mockScopeTag2, mockScopeTag3]),
      getAltTypeTags: vi.fn(() => []),
      hasSubTag: vi.fn((subCode: string) =>
        [mockScopeTag1, mockScopeTag2, mockScopeTag3].includes(subCode)
      ),
    },
  };

  const createMockProps = (overrides: Partial<CardProps> = {}): CardProps => ({
    surveyData: createSurveyData({
      scope: getDefaultScope(),
      scopeConfig: [],
      ...overrides.surveyData,
    }),
    appSettings: {
      availableAddons: [],
      settingsRecommendedAddons: [],
      overviewRecommendedAddons: [],
      ...overrides.appSettings,
    },
    questionList: [mockQuestion] as any,
    UNSDGMap: { goals: [] },
    blueprint: {},
    view: ViewValues.QuestionPacks,
    cardGroup: ViewValues.QuestionPacks,
    scopeType: ViewValues.QuestionPacks,
    addBtn: vi.fn(),
    breadcrumbs: [],
    metricGroups: [],
    handleDrilldown: vi.fn(),
    users: [],
    selectedView: ViewValues.QuestionPacks,
    setSelectedView: vi.fn(),
    scopeFilters: { filterByMateriality: [] },
    updateScopeFilters: vi.fn(),
    ...overrides,
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return three card groups: core, free, and premium', () => {
    const props = createMockProps();
    const result = getQuestionPackCards(props);

    expect(result).toHaveLength(3);
    expect(result[0]).toHaveProperty('cards');
    expect(result[1]).toHaveProperty('name', CardGroupName.Free);
    expect(result[1]).toHaveProperty('component');
    expect(result[2]).toHaveProperty('name', CardGroupName.Premium);
  });

  it('should include required scope configs in core cards with isMandatory flag', () => {
    const props = createMockProps({
      surveyData: createSurveyData({
        scope: getDefaultScope(),
        scopeConfig: [
          createMockScopeConfig(mockScopeTag1, true),
        ],
      }),
    });

    const result = getQuestionPackCards(props);
    const coreCards = result[0].cards;

    const coreCard = coreCards.find((card) => card.scopeTag === mockScopeTag1);
    expect(coreCard).toBeDefined();
    expect(coreCard?.isMandatory).toBe(true);
  });

  it('should include recommended addons in core cards with isMandatory flag', () => {
    const props = createMockProps({
      appSettings: {
        availableAddons: [],
        settingsRecommendedAddons: [mockScopeTag2],
        overviewRecommendedAddons: [],
      },
    });

    const result = getQuestionPackCards(props);
    const coreCards = result[0].cards;

    const coreCard = coreCards.find((card) => card.scopeTag === mockScopeTag2);
    expect(coreCard).toBeDefined();
    expect(coreCard?.isMandatory).toBe(true);
  });

  it('should categorize cards with premium tags as premium cards', () => {
    const props = createMockProps({
      appSettings: {
        availableAddons: [mockScopeTag2],
        settingsRecommendedAddons: [],
        overviewRecommendedAddons: [],
      },
    });

    const result = getQuestionPackCards(props);
    const premiumCards = result[2].cards;

    expect(premiumCards.some((card) => card.scopeTag === mockScopeTag2)).toBe(true);
  });

  it('should categorize cards without premium tags as free cards', () => {
    const props = createMockProps({
      appSettings: {
        availableAddons: [mockScopeTag1],
        settingsRecommendedAddons: [],
        overviewRecommendedAddons: [],
      },
    });

    const result = getQuestionPackCards(props);
    const freeCards = result[1].cards;

    expect(freeCards.some((card) => card.scopeTag === mockScopeTag1)).toBe(true);
  });

  it('should include cards that are in scope in free or premium cards', () => {
    const props = createMockProps({
      surveyData: createSurveyData({
        scope: {
          ...getDefaultScope(),
          standards: [mockScopeTag3],
        },
      }),
    });
    const result = getQuestionPackCards(props);
    const freeCards = result[1].cards;

    expect(freeCards.some((card) => card.scopeTag === mockScopeTag3)).toBe(true);
  });

  it('should show all packs in core for restricted tiers', () => {
    const props = createMockProps({
      isRestricted: true,
      surveyData: {
        _id: 'survey-123',
        initiativeId: 'initiative-456',
        scope: getDefaultScope(),
        scopeConfig: [
          createMockScopeConfig(mockScopeTag1, false),
          createMockScopeConfig(mockScopeTag2, false),
        ],
      },
    });

    const result = getQuestionPackCards(props);
    const coreCards = result[0].cards;

    expect(coreCards.some((card) => card.scopeTag === mockScopeTag1)).toBe(true);
    expect(coreCards.some((card) => card.scopeTag === mockScopeTag2)).toBe(true);
  });

  it('should add "Create a new metric group" card to core cards', () => {
    const props = createMockProps();
    const result = getQuestionPackCards(props);
    const coreCards = result[0].cards;

    const createCard = coreCards.find((card) => card.key === 'scope-cardgriditem-metricgroup-createnew');
    expect(createCard).toBeDefined();
    expect(createCard?.buttons).toHaveLength(1);
    expect(createCard?.buttons?.[0].to).toContain('create');
  });

  it('should include custom metric cards in core cards', () => {
    const mockCustomMetricCard = createMockCard('custom-metric-1');

    mockGetRequiredCustomMetrics.mockReturnValue([
      {
        cards: [mockCustomMetricCard],
      },
    ]);

    const props = createMockProps();
    const result = getQuestionPackCards(props);
    const coreCards = result[0].cards;

    expect(coreCards.some((card) => card.scopeTag === 'custom-metric-1')).toBe(true);

    // Reset mock
    mockGetRequiredCustomMetrics.mockReturnValue([]);
  });

  it('should show archived cards when selectedView is Archived', () => {
    const props = createMockProps({
      selectedView: ViewValues.Archived,
    });

    const result = getQuestionPackCards(props);

    expect(result[1].name).toBe(CardGroupName.Archived);
  });

  it('should call setSelectedView when provided', () => {
    const mockSetSelectedView = vi.fn();
    const props = createMockProps({
      setSelectedView: mockSetSelectedView,
    });

    getQuestionPackCards(props);

    expect(mockSetSelectedView).not.toHaveBeenCalled();
  });

  it('should use default selectedView when not provided', () => {
    const props = createMockProps({
      selectedView: undefined,
    });

    const result = getQuestionPackCards(props);

    expect(result[1].name).toBe(CardGroupName.Free);
  });

  it('should handle empty scopeConfig', () => {
    const props = createMockProps({
      surveyData: {
        _id: 'survey-123',
        initiativeId: 'initiative-456',
        scope: createMockScope(),
        scopeConfig: undefined,
      },
    });

    expect(() => getQuestionPackCards(props)).not.toThrow();
  });

  it('should filter cards correctly with availableAddons', () => {
    const props = createMockProps({
      appSettings: {
        availableAddons: [mockScopeTag1, mockScopeTag3],
        settingsRecommendedAddons: [],
        overviewRecommendedAddons: [],
      },
    });

    const result = getQuestionPackCards(props);
    const freeCards = result[1].cards;

    expect(freeCards.some((card) => card.scopeTag === mockScopeTag1)).toBe(true);
    expect(freeCards.some((card) => card.scopeTag === mockScopeTag3)).toBe(true);
  });

  it('should not duplicate cards between core and free/premium groups', () => {
    const props = createMockProps({
      surveyData: {
        _id: 'survey-123',
        initiativeId: 'initiative-456',
        scope: getDefaultScope(),
        scopeConfig: [
          createMockScopeConfig(mockScopeTag1, true),
        ],
      },
      appSettings: {
        availableAddons: [mockScopeTag1],
        settingsRecommendedAddons: [],
        overviewRecommendedAddons: [],
      },
    });

    const result = getQuestionPackCards(props);
    const coreCards = result[0].cards;
    const freeCards = result[1].cards;

    const inCore = coreCards.some((card) => card.scopeTag === mockScopeTag1);
    const inFree = freeCards.some((card) => card.scopeTag === mockScopeTag1);

    expect(inCore).toBe(true);
    expect(inFree).toBe(false);
  });
});