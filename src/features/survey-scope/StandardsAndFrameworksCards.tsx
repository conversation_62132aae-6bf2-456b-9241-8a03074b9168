/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CardGridButtonProps, CardGridGroup, CardGridItemProps } from './CardGrid';
import { frameworks, Group, ratings, regulatory, standards } from '@g17eco/core';
import { CardProps } from '@g17eco/types/surveyScope';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { filterStandardAndFramework } from '@utils/survey/filters';
import { ROUTES } from '../../constants/routes';
import { generateUrl } from '../../routes/util';
import { getScopeInfo } from './scopeSelection';
import { hasRequiredTags } from '../../constants/groups';
import { getCardGroups } from './cardGroups';
import { CardGroupName, getSortedCards, getSortedCustomMetricCards } from './utils';
import { SurveyCardsFilter } from './partials/SurveyCardsFilter';
import { getRequiredCustomMetrics } from './partials/RequiredCustomMetrics';
import { QUESTION } from '@constants/terminology';
import { hasMateriality } from '@utils/materiality';
import { BaseScopeQuestion } from '@g17eco/types/survey';
import { ViewMap } from '../../constants/viewOptions';

export interface PackAccumulator {
  latest: CardGridItemProps[];
  archived: CardGridItemProps[];
  latestMap: { [key: string]: CardGridItemProps };
}
export const filterCombinedCards = (combinedCards: CardGridItemProps[]) => {
  return combinedCards.reduce(
    (acc, pack) => {
      if (pack.archived) {
        acc.archived.push(pack);
        return acc;
      }
      
      if (!pack.versionGroupCode || !pack.version) {
        acc.latest.push(pack);
        return acc;
      }
      
      /** @deprecated since we introduced archived flag, we could remove this check */
      /** Never run into this case because above it already return when archived, but keeping for now */
      const duplicatedPack = acc.latestMap[pack.versionGroupCode];
      if (duplicatedPack) {
        if (pack.version > (duplicatedPack.version || 0)) {
          acc.latest = acc.latest.filter(
            (existedPack) => existedPack.versionGroupCode !== duplicatedPack.versionGroupCode,
          );
          acc.latest.push(pack);
          acc.archived.push(duplicatedPack);
        } else {
          acc.archived.push(pack);
        }
      } else {
        acc.latest.push(pack);
        acc.latestMap[pack.versionGroupCode] = pack;
      }

      return acc;
    },
    { latest: [], archived: [], latestMap: {} } as PackAccumulator,
  );
};

export const getStandardsCards = (props: CardProps) => _getCards(standards, props);
export const getFrameworksCards = (props: CardProps) => _getCards(frameworks, props);
export const getRegulatoryCards = (props: CardProps) => _getCards(regulatory, props);
export const getRatingsCards = (props: CardProps) => _getCards(ratings, props);

export const getQuestionPackCards = (props: CardProps) => {
  const {
    surveyData,
    appSettings,
    selectedView = ViewValues.QuestionPacks,
    setSelectedView = () => {},
    breadcrumbs,
    materiality,
    isRestricted,
  } = props;
  const options = [ViewValues.StandardsAndFrameworks, ViewValues.Archived].map((k: ViewValues) => ViewMap[k]);

  const coreCards: CardGridItemProps[] = [];
  const freeCards: CardGridItemProps[] = [];
  const premiumCards: CardGridItemProps[] = [];
  const recommendedAddons = appSettings.settingsRecommendedAddons;

  const coreAddons: string[] = [];
  if (surveyData.scopeConfig) {
    surveyData.scopeConfig.forEach((s) => {
      // currently restricted tiers (e.g. Starter, Toli) has cardGroup: 'question-packs' view (not affect other views like: 'all-packs')
      // show all packs in CORE cards from surveyData.scopeConfig for restricted tiers
      // even packs are not required (as FREE and PREMIUM cards are hidden)
      if (s.required || isRestricted) {
        coreAddons.push(s.code);
      }
    });
  }

  const availableAddons: string[] = appSettings.availableAddons ?? [];
  const allCards = getStandardsAndFrameworksCards(props);

  allCards.forEach((cardGroup) => {
    cardGroup.cards.forEach((c) => {
      if (coreAddons.includes(c.scopeTag) || recommendedAddons.includes(c.scopeTag)) {
        return coreCards.push({
          ...c,
          isMandatory: true,
        });
      }
      const { inScope } = getScopeInfo(surveyData.scope, c.scopeTag, ViewValues.StandardsAndFrameworks, breadcrumbs, {
        materiality,
      });
      if (availableAddons.includes(c.scopeTag) || inScope) {
        if (hasRequiredTags(c.requiredTags)) {
          premiumCards.push(c);
        } else {
          freeCards.push(c);
        }
      }
    });
  });

  const displayedCards =
    selectedView !== ViewValues.Archived
      ? freeCards
      : getStandardsAndFrameworksCards({ ...props, isArchived: true })[0].cards;

  const customMetrics = getRequiredCustomMetrics(props);

  customMetrics.forEach((cardGroup) => {
    cardGroup.cards.forEach((card) => coreCards.push(card));
  });

  coreCards.push({
    key: 'scope-cardgriditem-metricgroup-createnew',
    title: (
      <>
        <span>Create a new metric group</span>
      </>
    ),
    sortTitle: 'zzz Create a new metric group',
    subtitle: 'Click here if you would like to create a new group of custom metrics',
    icon: <i className={'fal fa-file-circle-question'} />,
    unitCount: undefined,
    unitName: '',
    buttons: [
      {
        icon: <span className='px-3'>Create</span>,
        tooltip: `Create or Manage metric ${QUESTION.SINGULAR} groups`,
        to: `${generateUrl(ROUTES.CUSTOM_METRICS, {
          initiativeId: surveyData.initiativeId,
          groupId: 'create',
        })}?surveyId=${surveyData._id}`,
      },
    ],
    scopeTag: '',
    inScope: false,
    isPartial: false,
  });

  const cardGroups: CardGridGroup[] = [
    {
      cards: coreCards,
    },
    {
      name: selectedView === ViewValues.Archived ? CardGroupName.Archived : CardGroupName.Free,
      cards: displayedCards,
      component: () => (
        <SurveyCardsFilter
          options={options}
          selectedView={selectedView}
          setSelectedView={setSelectedView}
          hasMateriality={hasMateriality(materiality)}
        />
      ),
    },
    {
      name: CardGroupName.Premium,
      cards: premiumCards,
    },
  ];

  return cardGroups;
};

export const getAllCards = (props: CardProps) => {
  const {
    surveyData,
    appSettings,
    selectedView = ViewValues.StandardsAndFrameworks,
    setSelectedView = () => {},
    scopeFilters,
    updateScopeFilters,
    materiality,
  } = props;

  const options = [ViewValues.StandardsAndFrameworks, ViewValues.Sdg, ViewValues.Archived].map(
    (k: ViewValues) => ViewMap[k],
  );

  const coreCards: CardGridItemProps[] = [];
  const premiumCards: CardGridItemProps[] = [];
  const recommendedAddons = appSettings.settingsRecommendedAddons;

  const requiredAddons: string[] = [];
  if (surveyData.scopeConfig) {
    surveyData.scopeConfig.forEach((s) => {
      if (s.required) {
        requiredAddons.push(s.code);
      }
    });
  }

  const standardsFrameworksCards = getStandardsAndFrameworksCards(props);

  standardsFrameworksCards
    .reduce((a, c) => a.concat(c.cards), [] as CardGridItemProps[])
    .forEach((c) => {
      switch (true) {
        case requiredAddons.includes(c.scopeTag) || recommendedAddons.includes(c.scopeTag):
          coreCards.push({
            ...c,
            isMandatory: true,
          });
          break;
        case hasRequiredTags(c.requiredTags):
          premiumCards.push(c);
          break;
        default:
          break;
      }
    });

  const excludedScopeTags = [...coreCards, ...premiumCards].map((card) => card.scopeTag);
  const selectedViewCards = getCardGroups({ ...props, cardGroup: selectedView }).reduce(
    (a, c) => a.concat(c.cards),
    [] as CardGridItemProps[],
  );
  const displayedCards = selectedViewCards.filter((card) => {
    return !excludedScopeTags.includes(card.scopeTag);
  });

  const customMetrics = getRequiredCustomMetrics(props);
  const customMetricCards = getSortedCustomMetricCards(customMetrics, surveyData.initiativeId);

  coreCards.push(...customMetricCards);

  coreCards.push({
    key: 'scope-cardgriditem-metricgroup-createnew',
    title: (
      <>
        <span>Create a new metric group</span>
      </>
    ),
    sortTitle: 'zzz Create a new metric group',
    subtitle: 'Click here if you would like to create a new group of custom metrics',
    icon: <i className={'fal fa-file-circle-question'} />,
    unitCount: undefined,
    unitName: '',
    buttons: [
      {
        icon: <span className='px-3'>Create</span>,
        tooltip: `Create or Manage metric ${QUESTION.SINGULAR} groups`,
        to: `${generateUrl(ROUTES.CUSTOM_METRICS, {
          initiativeId: surveyData.initiativeId,
          groupId: 'create',
        })}?surveyId=${surveyData._id}`,
      },
    ],
    scopeTag: '',
    inScope: false,
    isPartial: false,
  });
  
  const displayedCardGroupName = props.selectedView === ViewValues.Archived ? CardGroupName.Archived : CardGroupName.Free;
  const cardGroups: CardGridGroup[] = [
    {
      cards: getSortedCards(coreCards),
    },
    {
      name: displayedCardGroupName,
      cards: getSortedCards(displayedCards, displayedCardGroupName),
      component: () => (
        <SurveyCardsFilter
          options={options}
          selectedView={selectedView}
          setSelectedView={setSelectedView}
          filterByMateriality={scopeFilters.filterByMateriality}
          updateFilterMateriality={(values) => updateScopeFilters('filterByMateriality', values)}
          hasMateriality={hasMateriality(materiality)}
        />
      ),
    },
    {
      name: CardGroupName.Premium,
      cards: getSortedCards(premiumCards, CardGroupName.Premium),
    },
  ];

  return cardGroups;
};

export const getStandardsAndFrameworksCards = (props: CardProps & { isArchived?: boolean }) => {
  const standardsCards = getStandardsCards(props);
  const frameworksCards = getFrameworksCards(props);

  const combinedCards: CardGridItemProps[] = [];
  frameworksCards.map((framework) => framework.cards.map((c) => combinedCards.push(c)));
  standardsCards.map((standard) => standard.cards.map((c) => combinedCards.push(c)));

  const filteredCombinedCards = filterCombinedCards(combinedCards);

  return [
    {
      cards: props.isArchived ? filteredCombinedCards.archived : filteredCombinedCards.latest,
    },
  ];
};

const _getCards = (groups: { [key: string]: Group }, props: CardProps) => {
  const { questionList, addBtn, handleDrilldown, breadcrumbs, surveyData, scopeType, materiality } = props;
  const cardGroups: CardGridGroup[] = [];
  const cards: CardGridItemProps[] = [];
  const isDrilldown = breadcrumbs && breadcrumbs.length > 0;

  const getButtons = (group: Group) => {
    const code = group.code;
    const name = group.name;
    const buttons: CardGridButtonProps[] = [];

    const { inScope } = getScopeInfo(surveyData.scope, code, scopeType, breadcrumbs, { materiality });

    const hasRequiredScope =
      surveyData.scopeConfig && surveyData.scopeConfig.some((scope) => scope.code === code && scope.required);

    if (group.link) {
      buttons.push({
        icon: <i className='fa fa-link text-ThemeTextPlaceholder' />,
        tooltip: group.link,
        onClick: () => window.open(group.link),
      });
    }

    if (isDrilldown) {
      buttons.push({
        icon: <i className='fa fa-eye text-ThemeTextPlaceholder' />,
        tooltip: `${name} ${QUESTION.CAPITALIZED_PLURAL} - Click to view`,
        onClick: () => handleDrilldown(ViewValues.QuestionList, code, name, ViewValues.Standards),
      });
    } else if (group && group.subgroups && group.subgroups.length > 0) {
      buttons.push({
        icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
        tooltip: `${name} Groups - Click to view`,
        onClick: () => handleDrilldown(ViewValues.Group, code, name),
      });
    } else {
      buttons.push({
        icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
        tooltip: `${name} ${QUESTION.CAPITALIZED_PLURAL} - Click to view`,
        onClick: () => handleDrilldown(ViewValues.QuestionList, code, name),
      });
    }

    if (inScope && hasRequiredScope) {
      return buttons;
    }

    if (!isDrilldown) {
      const addButton = addBtn(code, name, group);
      if (addButton) {
        buttons.push(addButton);
      }
    }
    return buttons;
  };

  Object.keys(groups).forEach((scopeTag: string, i: number) => {
    const group = groups[scopeTag];
    const { inScope, isPartial } = getScopeInfo(
      surveyData.scope,
      scopeTag,
      ViewValues.StandardsAndFrameworks,
      breadcrumbs,
      { materiality }
    );
    const isPartialScope = inScope || isPartial;

    if (group.hidden && !isPartialScope) {
      return;
    }

    const filteredQuestionList: BaseScopeQuestion[] = questionList.filter((question) =>
      filterStandardAndFramework(question, scopeTag),
    );
    if (filteredQuestionList.length === 0) {
      return;
    }
    cards.push({
      key: `scope-cardgriditem-sandf-${scopeTag}-${i}`,
      title: (
        <>
          <span>{group.name}</span>
        </>
      ),
      sortTitle: `${group.name}-${group.code}`,
      subtitle: group.subtitle,
      description: group.description ?? '',
      icon: <img src={group.src} alt={group.name} width='40px' />,
      unitCount: filteredQuestionList.length,
      buttons: getButtons(group),
      scopeTag,
      requiredTags: group.requiredTags,
      inScope,
      isPartial,
      version: group.version,
      versionGroupCode: group.versionGroupCode,
      archived: group.archived,
    });
  });
  cardGroups.push({
    cards,
  });
  return cardGroups;
};
